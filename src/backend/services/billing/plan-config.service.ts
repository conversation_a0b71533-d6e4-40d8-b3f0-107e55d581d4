import { env } from '../../config/env.js';

export interface PlanConfig {
  name: string;
  monthlyEmailLimit: number;
  domains: number;
  aliases: number;
  webhooks: number;
  features: string[];
  price?: {
    monthly: number;
    yearly: number;
    currency: string;
  };
}

// Permission system types
export type PlanPermission =
  | 'custom_headers'
  | 'priority_support'
  | 'email_analytics'
  | 'custom_integrations'
  | 'sla_guarantee'
  | 'premium_delivery';

export type PlanType = 'free' | 'pro' | 'enterprise';

// Plan permissions configuration
export const PLAN_PERMISSIONS: Record<PlanType, readonly PlanPermission[]> = {
  free: [],
  pro: ['custom_headers', 'priority_support', 'premium_delivery'],
  enterprise: ['custom_headers', 'priority_support', 'email_analytics', 'custom_integrations', 'sla_guarantee', 'premium_delivery']
} as const;

// FIXED: API Scope to Plan Permission mapping for unified validation
// NOTE: Basic operations (read/write) are available to all plans subject to quantity limits.
// Only advanced features require specific plan permissions.
export const SCOPE_PLAN_REQUIREMENTS: Record<string, {
  requiredPermissions: PlanPermission[];
  minimumPlan?: PlanType;
  description: string;
}> = {
  // Webhook scopes - FIXED: Basic webhook operations available to all plans
  'webhooks:write': {
    requiredPermissions: [],  // ✅ FIXED: Basic webhook creation available to all plans
    description: 'Basic webhook creation available on all plans (subject to quantity limits)'
  },
  'webhooks:read': {
    requiredPermissions: [],
    description: 'Reading webhooks available on all plans'
  },

  // Domain scopes - FIXED: Basic domain operations available to all plans
  'domains:write': {
    requiredPermissions: [],
    description: 'Domain creation and basic configuration available on all plans (subject to quantity limits)'
  },
  'domains:config': {
    requiredPermissions: [],  // ✅ FIXED: Basic domain config available to all plans
    description: 'Basic domain configuration available on all plans'
  },
  'domains:read': {
    requiredPermissions: [],
    description: 'Reading domains available on all plans'
  },
  'domains:status': {
    requiredPermissions: [],
    description: 'Domain status check available on all plans'
  },

  // Alias scopes
  'aliases:write': {
    requiredPermissions: [],
    description: 'Alias creation available on all plans (subject to quantity limits)'
  },
  'aliases:read': {
    requiredPermissions: [],
    description: 'Reading aliases available on all plans'
  },

  // Enterprise-only scopes
  'analytics:read': {
    requiredPermissions: ['email_analytics'],
    minimumPlan: 'enterprise',
    description: 'Email analytics requires Enterprise plan'
  },
  'integrations:write': {
    requiredPermissions: ['custom_integrations'],
    minimumPlan: 'enterprise',
    description: 'Custom integrations require Enterprise plan'
  }
} as const;

export interface PlanLimits {
  monthlyEmails: number;
  emails: number; // Alias for monthlyEmails for consistency
  domains: number;
  webhooks: number;
  aliases: number;
}

export interface CreditPricing {
  pricePerHundred: number; // Price in EUR for 100 email credits
  currency: string;
}

export class PlanConfigService {
  private static readonly PLAN_CONFIGS: Record<string, PlanConfig> = {
    free: {
      name: 'Free',
      monthlyEmailLimit: 50,
      domains: 1,
      aliases: 2,
      webhooks: 2,
      features: [
        'Up to 50 emails per month',
        '1 domain',
        '2 aliases total',
        '2 webhooks total',
        'Basic webhook delivery',
        'In-line attachments (<128Kb)',
        'Additional credits at €1.00/100 emails'
      ]
    },
    pro: {
      name: 'Pro',
      monthlyEmailLimit: 1000,
      domains: 5,
      aliases: 50,
      webhooks: 50,
      features: [
        'Up to 1,000 emails per month',
        '5 domains',
        '50 aliases per domain',
        '50 webhooks with custom headers',
        'Discounted credit purchases (€0.80/100 emails)'
      ],
      price: {
        monthly: 9.95,
        yearly: 99.50,
        currency: 'EUR'
      }
    },
    enterprise: {
      name: 'Enterprise',
      monthlyEmailLimit: 10000,
      domains: 100,
      aliases: 500,
      webhooks: 100,
      features: [
        'Up to 10,000 emails per month',
        'Any volume of domains',
        'Premium webhook delivery',
        'Email analytics dashboard',
        'Custom integrations',
        'Advanced reporting',
        'Dedicated support',
        'SLA guarantee'
      ],
      price: {
        monthly: 49.95,
        yearly: 499.50,
        currency: 'EUR'
      }
    }
  };

  /**
   * Get configuration for a specific plan
   */
  static getPlanConfig(planType: string): PlanConfig {
    const config = this.PLAN_CONFIGS[planType];
    if (!config) {
      throw new Error(`Unknown plan type: ${planType}`);
    }
    return config;
  }

  /**
   * Get all available plans
   */
  static getAllPlans(): Record<string, PlanConfig> {
    return { ...this.PLAN_CONFIGS };
  }

  /**
   * Get plan limits for a specific plan type
   */
  static getPlanLimits(planType: string, domainCount: number = 1): PlanLimits {
    const config = this.getPlanConfig(planType);

    if (planType === 'free') {
      return {
        monthlyEmails: config.monthlyEmailLimit,
        emails: config.monthlyEmailLimit,
        domains: config.domains,
        webhooks: config.webhooks,
        aliases: config.aliases
      };
    }

    if (planType === 'pro') {
      return {
        monthlyEmails: config.monthlyEmailLimit,
        emails: config.monthlyEmailLimit,
        domains: config.domains,
        webhooks: domainCount * config.webhooks, // webhooks per domain
        aliases: domainCount * config.aliases     // aliases per domain
      };
    }

    // Default fallback (shouldn't reach here with current plans)
    return {
      monthlyEmails: config.monthlyEmailLimit,
      emails: config.monthlyEmailLimit,
      domains: config.domains,
      webhooks: config.webhooks,
      aliases: config.aliases
    };
  }

  /**
   * Get credit pricing for a specific plan type
   */
  static getCreditPricing(planType: string): CreditPricing {
    // Pro and Enterprise users get discounted credit pricing
    const hasDiscountedCredits = this.userHasPermission(planType, 'priority_support');
    return {
      pricePerHundred: hasDiscountedCredits ? 0.80 : 1.00,
      currency: 'EUR'
    };
  }

  /**
   * Check if a plan upgrade is valid
   */
  static isValidUpgrade(currentPlan: string, targetPlan: string): boolean {
    const planHierarchy = ['free', 'pro', 'enterprise'];
    const currentIndex = planHierarchy.indexOf(currentPlan);
    const targetIndex = planHierarchy.indexOf(targetPlan);
    
    return currentIndex !== -1 && targetIndex !== -1 && targetIndex > currentIndex;
  }

  /**
   * Check if a plan downgrade is valid
   */
  static isValidDowngrade(currentPlan: string, targetPlan: string): boolean {
    const planHierarchy = ['free', 'pro', 'enterprise'];
    const currentIndex = planHierarchy.indexOf(currentPlan);
    const targetIndex = planHierarchy.indexOf(targetPlan);

    return currentIndex !== -1 && targetIndex !== -1 && targetIndex < currentIndex;
  }

  /**
   * Check if a user has a specific permission based on their plan type
   */
  static userHasPermission(planType: string, permission: PlanPermission): boolean {
    const planPermissions = PLAN_PERMISSIONS[planType as PlanType];
    return planPermissions?.includes(permission) ?? false;
  }

  /**
   * Get all permissions for a specific plan type
   */
  static getPlanPermissions(planType: string): readonly PlanPermission[] {
    return PLAN_PERMISSIONS[planType as PlanType] ?? [];
  }

  /**
   * Check if a user has any of the specified permissions
   */
  static userHasAnyPermission(planType: string, permissions: PlanPermission[]): boolean {
    return permissions.some(permission => this.userHasPermission(planType, permission));
  }

  /**
   * Check if a user has all of the specified permissions
   */
  static userHasAllPermissions(planType: string, permissions: PlanPermission[]): boolean {
    return permissions.every(permission => this.userHasPermission(planType, permission));
  }

  /**
   * Get missing permissions for a user to perform an action
   */
  static getMissingPermissions(planType: string, requiredPermissions: PlanPermission[]): PlanPermission[] {
    return requiredPermissions.filter(permission => !this.userHasPermission(planType, permission));
  }

  /**
   * UPDATED: Check if an API scope is allowed for a plan type
   */
  static validateScopeForPlan(scope: string, planType: PlanType): {
    allowed: boolean;
    reason?: string;
    requiredPlan?: PlanType;
    missingPermissions?: PlanPermission[];
  } {
    const requirements = SCOPE_PLAN_REQUIREMENTS[scope];
    
    if (!requirements) {
      // If no specific requirements defined, scope is allowed
      return { allowed: true };
    }

    // Check if user has required permissions
    const missingPermissions = this.getMissingPermissions(planType, requirements.requiredPermissions);
    if (missingPermissions.length > 0) {
      return {
        allowed: false,
        reason: `${scope} requires ${missingPermissions.join(', ')} permissions`,
        requiredPlan: requirements.minimumPlan,
        missingPermissions
      };
    }

    // Check minimum plan requirement
    if (requirements.minimumPlan && !this.meetsPlanRequirement(planType, requirements.minimumPlan)) {
      return {
        allowed: false,
        reason: `${scope} requires ${requirements.minimumPlan}+ plan`,
        requiredPlan: requirements.minimumPlan
      };
    }

    return { allowed: true };
  }

  /**
   * Check if current plan meets minimum requirement
   */
  private static meetsPlanRequirement(currentPlan: PlanType, requiredPlan: PlanType): boolean {
    const hierarchy: PlanType[] = ['free', 'pro', 'enterprise'];
    const currentIndex = hierarchy.indexOf(currentPlan);
    const requiredIndex = hierarchy.indexOf(requiredPlan);
    return currentIndex >= requiredIndex;
  }

  /**
   * Get the default monthly email limit for new users
   */
  static getDefaultEmailLimit(): number {
    return env.DEFAULT_MONTHLY_EMAIL_LIMIT;
  }

  /**
   * Validate if current usage fits within target plan limits
   */
  static validateUsageForPlan(
    planType: string,
    currentUsage: {
      domains: number;
      webhooks: number;
      aliases: number;
    }
  ): { valid: boolean; violations: string[] } {
    const limits = this.getPlanLimits(planType, currentUsage.domains);
    const violations: string[] = [];

    if (currentUsage.domains > limits.domains) {
      violations.push(`Too many domains (${currentUsage.domains}/${limits.domains})`);
    }

    if (currentUsage.webhooks > limits.webhooks) {
      violations.push(`Too many webhooks (${currentUsage.webhooks}/${limits.webhooks})`);
    }

    if (currentUsage.aliases > limits.aliases) {
      violations.push(`Too many aliases (${currentUsage.aliases}/${limits.aliases})`);
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  /**
   * UPDATED: Get scope requirements for documentation/UI
   */
  static getScopeRequirements(): typeof SCOPE_PLAN_REQUIREMENTS {
    return SCOPE_PLAN_REQUIREMENTS;
  }

  /**
   * Get plan comparison data for upgrade prompts
   */
  static getPlanComparison(currentPlan: PlanType): {
    current: PlanConfig;
    upgrades: Array<{ plan: PlanType; config: PlanConfig; benefits: string[] }>;
  } {
    const current = this.getPlanConfig(currentPlan);
    const upgrades: Array<{ plan: PlanType; config: PlanConfig; benefits: string[] }> = [];

    if (currentPlan === 'free') {
      upgrades.push({
        plan: 'pro',
        config: this.getPlanConfig('pro'),
        benefits: [
          'Custom webhook headers',
          'Advanced domain configuration',
          'Priority support',
          '20x more emails (1,000/month)',
          '5 domains, 50 aliases, 50 webhooks'
        ]
      });
      upgrades.push({
        plan: 'enterprise',
        config: this.getPlanConfig('enterprise'),
        benefits: [
          'Everything in Pro',
          'Email analytics dashboard',
          'Custom integrations',
          'SLA guarantee',
          '200x more emails (10,000/month)',
          'Unlimited domains'
        ]
      });
    } else if (currentPlan === 'pro') {
      upgrades.push({
        plan: 'enterprise',
        config: this.getPlanConfig('enterprise'),
        benefits: [
          'Email analytics dashboard',
          'Custom integrations',
          'Advanced reporting',
          'SLA guarantee',
          '10x more emails (10,000/month)',
          'Unlimited domains'
        ]
      });
    }

    return { current, upgrades };
  }
}
