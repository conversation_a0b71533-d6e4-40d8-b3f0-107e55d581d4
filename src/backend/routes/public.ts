import { FastifyPluginAsync } from 'fastify';
import { PlanConfigService } from '../services/billing/plan-config.service.js';
import { ContentService } from '../services/content.service.js';

export const publicRoutes: FastifyPluginAsync = async (fastify) => {
  const contentService = new ContentService();
  // Get public plan configurations for pricing display
  fastify.get('/plans', {
    schema: {
      tags: ['Public'],
      summary: 'Get public plan configurations',
      description: 'Retrieve plan configurations for public pricing display',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            plans: {
              type: 'object',
              properties: {
                free: {
                  type: 'object',
                  properties: {
                    name: { type: 'string' },
                    monthlyEmailLimit: { type: 'integer' },
                    domains: { type: 'integer' },
                    aliases: { type: 'integer' },
                    webhooks: { type: 'integer' },
                    features: {
                      type: 'array',
                      items: { type: 'string' }
                    }
                  }
                },
                pro: {
                  type: 'object',
                  properties: {
                    name: { type: 'string' },
                    monthlyEmailLimit: { type: 'integer' },
                    domains: { type: 'integer' },
                    aliases: { type: 'integer' },
                    webhooks: { type: 'integer' },
                    features: {
                      type: 'array',
                      items: { type: 'string' }
                    },
                    price: {
                      type: 'object',
                      properties: {
                        monthly: { type: 'number' },
                        yearly: { type: 'number' },
                        currency: { type: 'string' }
                      }
                    }
                  }
                }
              }
            },
            creditPricing: {
              type: 'object',
              properties: {
                free: {
                  type: 'object',
                  properties: {
                    pricePerHundred: { type: 'number' },
                    currency: { type: 'string' }
                  }
                },
                pro: {
                  type: 'object',
                  properties: {
                    pricePerHundred: { type: 'number' },
                    currency: { type: 'string' }
                  }
                }
              }
            }
          }
        },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, async (request, reply) => {
    try {
      const freePlan = PlanConfigService.getPlanConfig('free');
      const proPlan = PlanConfigService.getPlanConfig('pro');
      const freeCreditPricing = PlanConfigService.getCreditPricing('free');
      const proCreditPricing = PlanConfigService.getCreditPricing('pro');

      return reply.send({
        success: true,
        plans: {
          free: freePlan,
          pro: proPlan
        },
        creditPricing: {
          free: freeCreditPricing,
          pro: proCreditPricing
        }
      });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to get plan configurations');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve plan configurations'
      });
    }
  });

  // Changelog endpoints
  fastify.get('/changelog', {
    schema: {
      tags: ['Public'],
      summary: 'Get changelog entries',
      description: 'Retrieve all changelog entries for public display',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            entries: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  slug: { type: 'string' },
                  date: { type: 'string' },
                  type: { type: 'string', enum: ['added', 'changed', 'fixed'] },
                  title: { type: 'string' },
                  excerpt: { type: 'string' },
                  content: { type: 'string' }
                }
              }
            }
          }
        },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, async (request, reply) => {
    try {
      const entries = await contentService.getChangelogEntries();
      return reply.send({
        success: true,
        entries
      });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to get changelog entries');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve changelog entries'
      });
    }
  });

  // Help endpoints
  fastify.get('/help', {
    schema: {
      tags: ['Public'],
      summary: 'Get help articles',
      description: 'Retrieve all help articles for public display',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            articles: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  slug: { type: 'string' },
                  title: { type: 'string' },
                  excerpt: { type: 'string' },
                  category: { type: 'string' },
                  order: { type: 'number' }
                }
              }
            }
          }
        },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, async (request, reply) => {
    try {
      const articles = await contentService.getHelpArticles();
      // Return articles without full content for the index
      const articlesIndex = articles.map(article => ({
        slug: article.slug,
        title: article.title,
        excerpt: article.excerpt,
        category: article.category,
        order: article.order
      }));

      return reply.send({
        success: true,
        articles: articlesIndex
      });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to get help articles');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve help articles'
      });
    }
  });

  fastify.get('/help/:slug', {
    schema: {
      tags: ['Public'],
      summary: 'Get help article by slug',
      description: 'Retrieve a specific help article by its slug',
      params: {
        type: 'object',
        properties: {
          slug: { type: 'string' }
        },
        required: ['slug']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            article: {
              type: 'object',
              properties: {
                slug: { type: 'string' },
                title: { type: 'string' },
                excerpt: { type: 'string' },
                category: { type: 'string' },
                order: { type: 'number' },
                content: { type: 'string' }
              }
            }
          }
        },
        404: { $ref: 'ErrorResponse#' },
        500: { $ref: 'ErrorResponse#' }
      }
    }
  }, async (request, reply) => {
    try {
      const { slug } = request.params as { slug: string };
      const article = await contentService.getHelpArticleBySlug(slug);

      if (!article) {
        return reply.code(404).send({
          statusCode: 404,
          error: 'Not Found',
          message: 'Help article not found'
        });
      }

      return reply.send({
        success: true,
        article
      });
    } catch (error: any) {
      fastify.log.error({ error: error.message }, 'Failed to get help article');
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve help article'
      });
    }
  });
};
