<template>
  <div class="max-w-4xl mx-auto px-4 py-12">
    <div class="prose prose-lg max-w-none text-base-content">
      <h1 class="text-4xl font-bold text-base-content mb-8">Help center</h1>
      
      <p class="text-base-content/70 mb-8">
        Find guides, tutorials, and answers to common questions about EmailConnect.
      </p>

      <!-- Loading state -->
      <div v-if="loading" class="flex justify-center py-8">
        <div class="loading loading-spinner loading-lg"></div>
      </div>

      <!-- Error state -->
      <div v-else-if="error" class="alert alert-error mb-8">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>Failed to load help articles. Please try again later.</span>
      </div>

      <!-- Help articles by category -->
      <div v-else-if="articles.length > 0" class="space-y-8">
        <div v-for="(categoryArticles, category) in articlesByCategory" :key="category">
          <h2 class="text-2xl font-semibold text-base-content mb-4 capitalize">
            {{ formatCategoryName(category) }}
          </h2>
          
          <div class="grid gap-4 md:grid-cols-2">
            <router-link
              v-for="article in categoryArticles"
              :key="article.slug"
              :to="`/help/${article.slug}`"
              class="block p-6 border border-base-300 rounded-lg bg-base-100 hover:bg-base-200 transition-colors group"
            >
              <h3 class="text-lg font-semibold text-base-content mb-2 group-hover:text-primary">
                {{ article.title }}
              </h3>
              <p class="text-base-content/70 text-sm">
                {{ article.excerpt }}
              </p>
              <div class="flex items-center mt-3 text-primary text-sm">
                <span>Read more</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </router-link>
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div v-else class="text-center py-12">
        <div class="text-6xl mb-4">📚</div>
        <h3 class="text-xl font-semibold text-base-content mb-2">No help articles yet</h3>
        <p class="text-base-content/60">Documentation is coming soon. Check back later!</p>
      </div>

      <!-- Quick links section -->
      <div v-if="!loading && !error" class="mt-12 pt-8 border-t border-base-300">
        <h2 class="text-2xl font-semibold text-base-content mb-6">Quick links</h2>
        <div class="grid gap-4 md:grid-cols-3">
          <a 
            href="mailto:<EMAIL>"
            class="flex items-center p-4 border border-base-300 rounded-lg bg-base-100 hover:bg-base-200 transition-colors"
          >
            <div class="text-2xl mr-3">📧</div>
            <div>
              <div class="font-semibold text-base-content">Contact Support</div>
              <div class="text-sm text-base-content/60">Get help from our team</div>
            </div>
          </a>
          
          <router-link 
            to="/changelog"
            class="flex items-center p-4 border border-base-300 rounded-lg bg-base-100 hover:bg-base-200 transition-colors"
          >
            <div class="text-2xl mr-3">📝</div>
            <div>
              <div class="font-semibold text-base-content">Changelog</div>
              <div class="text-sm text-base-content/60">Latest updates & features</div>
            </div>
          </router-link>
          
          <a 
            href="https://status.emailconnect.eu" 
            target="_blank"
            rel="noopener noreferrer"
            class="flex items-center p-4 border border-base-300 rounded-lg bg-base-100 hover:bg-base-200 transition-colors"
          >
            <div class="text-2xl mr-3">🟢</div>
            <div>
              <div class="font-semibold text-base-content">Service Status</div>
              <div class="text-sm text-base-content/60">Check system status</div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface HelpArticle {
  slug: string
  title: string
  excerpt: string
  category: string
  order: number
}

const articles = ref<HelpArticle[]>([])
const loading = ref(true)
const error = ref(false)

const articlesByCategory = computed(() => {
  const grouped: Record<string, HelpArticle[]> = {}
  
  articles.value.forEach(article => {
    if (!grouped[article.category]) {
      grouped[article.category] = []
    }
    grouped[article.category].push(article)
  })
  
  // Sort articles within each category by order
  Object.keys(grouped).forEach(category => {
    grouped[category].sort((a, b) => a.order - b.order)
  })
  
  return grouped
})

const fetchHelpArticles = async () => {
  try {
    loading.value = true
    error.value = false
    
    const response = await fetch('/api/public/help')
    if (!response.ok) {
      throw new Error('Failed to fetch help articles')
    }
    
    const data = await response.json()
    articles.value = data.articles || []
  } catch (err) {
    console.error('Error fetching help articles:', err)
    error.value = true
  } finally {
    loading.value = false
  }
}

const formatCategoryName = (category: string) => {
  return category
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
}

onMounted(() => {
  fetchHelpArticles()
})
</script>

<style scoped>
/* Additional styles if needed */
</style>
