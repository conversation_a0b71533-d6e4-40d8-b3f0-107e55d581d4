<template>
  <div class="max-w-4xl mx-auto px-4 py-12">
    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center py-8">
      <div class="loading loading-spinner loading-lg"></div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="alert alert-error mb-8">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>{{ errorMessage }}</span>
    </div>

    <!-- Article content -->
    <div v-else-if="article" class="prose prose-lg max-w-none text-base-content">
      <!-- Breadcrumb navigation -->
      <nav class="flex items-center space-x-2 text-sm text-base-content/60 mb-8">
        <router-link to="/help" class="hover:text-primary">Help</router-link>
        <span>/</span>
        <span class="capitalize">{{ formatCategoryName(article.category) }}</span>
        <span>/</span>
        <span class="text-base-content">{{ article.title }}</span>
      </nav>

      <!-- Article header -->
      <div class="mb-8">
        <div class="flex items-center gap-2 mb-4">
          <span class="badge badge-primary badge-sm">{{ formatCategoryName(article.category) }}</span>
        </div>
        <h1 class="text-4xl font-bold text-base-content mb-4">{{ article.title }}</h1>
        <p class="text-lg text-base-content/70">{{ article.excerpt }}</p>
      </div>

      <!-- Article content -->
      <div 
        class="prose prose-lg max-w-none text-base-content"
        v-html="renderMarkdown(article.content)"
      ></div>

      <!-- Article footer -->
      <div class="mt-12 pt-8 border-t border-base-300">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div class="text-sm text-base-content/60">
            Was this article helpful? 
            <a href="mailto:<EMAIL>" class="text-primary hover:underline ml-1">
              Let us know
            </a>
          </div>
          
          <router-link 
            to="/help"
            class="btn btn-outline btn-sm"
          >
            ← Back to Help
          </router-link>
        </div>
      </div>
    </div>

    <!-- Not found state -->
    <div v-else class="text-center py-12">
      <div class="text-6xl mb-4">📄</div>
      <h1 class="text-2xl font-bold text-base-content mb-2">Article Not Found</h1>
      <p class="text-base-content/60 mb-6">The help article you're looking for doesn't exist or has been moved.</p>
      <router-link to="/help" class="btn btn-primary">
        Browse Help Articles
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'

interface HelpArticle {
  slug: string
  title: string
  excerpt: string
  category: string
  order: number
  content: string
}

const route = useRoute()
const article = ref<HelpArticle | null>(null)
const loading = ref(true)
const error = ref(false)
const errorMessage = ref('')

const fetchArticle = async (slug: string) => {
  try {
    loading.value = true
    error.value = false
    article.value = null
    
    const response = await fetch(`/api/public/help/${slug}`)
    
    if (response.status === 404) {
      // Article not found - this is handled by the template
      return
    }
    
    if (!response.ok) {
      throw new Error('Failed to fetch article')
    }
    
    const data = await response.json()
    article.value = data.article
  } catch (err) {
    console.error('Error fetching article:', err)
    error.value = true
    errorMessage.value = 'Failed to load the article. Please try again later.'
  } finally {
    loading.value = false
  }
}

const formatCategoryName = (category: string) => {
  return category
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
}

const renderMarkdown = (content: string) => {
  // Enhanced markdown rendering
  return content
    // Headers
    .replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold mt-8 mb-4">$1</h3>')
    .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold mt-10 mb-6">$1</h2>')
    .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mt-12 mb-8">$1</h1>')
    
    // Code blocks
    .replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre class="bg-base-200 rounded-lg p-4 overflow-x-auto my-4"><code>$2</code></pre>')
    .replace(/`([^`]+)`/gim, '<code class="bg-base-200 px-2 py-1 rounded text-sm">$1</code>')
    
    // Lists
    .replace(/^\* (.*)$/gim, '<li>$1</li>')
    .replace(/^- (.*)$/gim, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/gims, '<ul class="list-disc list-inside my-4 space-y-1">$1</ul>')
    
    // Bold and italic
    .replace(/\*\*(.*?)\*\*/gim, '<strong class="font-semibold">$1</strong>')
    .replace(/\*(.*?)\*/gim, '<em class="italic">$1</em>')
    
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" class="text-primary hover:underline">$1</a>')
    
    // Paragraphs
    .replace(/\n\n/gim, '</p><p class="mb-4">')
    .replace(/^(.*)$/gim, '<p class="mb-4">$1</p>')
    .replace(/^<p class="mb-4"><\/p>$/gim, '')
    
    // Clean up empty paragraphs and fix list formatting
    .replace(/<p class="mb-4">(<ul[\s\S]*?<\/ul>)<\/p>/gim, '$1')
    .replace(/<p class="mb-4">(<h[1-6][\s\S]*?<\/h[1-6]>)<\/p>/gim, '$1')
    .replace(/<p class="mb-4">(<pre[\s\S]*?<\/pre>)<\/p>/gim, '$1')
}

// Watch for route changes
watch(() => route.params.slug, (newSlug) => {
  if (newSlug && typeof newSlug === 'string') {
    fetchArticle(newSlug)
  }
}, { immediate: true })

onMounted(() => {
  const slug = route.params.slug
  if (slug && typeof slug === 'string') {
    fetchArticle(slug)
  }
})
</script>

<style scoped>
/* Additional styles for better markdown rendering */
:deep(ul) {
  @apply list-disc list-inside my-4 space-y-1;
}

:deep(ol) {
  @apply list-decimal list-inside my-4 space-y-1;
}

:deep(pre) {
  @apply bg-base-200 rounded-lg p-4 overflow-x-auto my-4;
}

:deep(code) {
  @apply bg-base-200 px-2 py-1 rounded text-sm;
}

:deep(blockquote) {
  @apply border-l-4 border-primary pl-4 my-4 italic;
}
</style>
