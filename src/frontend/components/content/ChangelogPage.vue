<template>
  <div class="max-w-4xl mx-auto px-4 py-12">
    <div class="prose prose-lg max-w-none text-base-content">
      <h1 class="text-4xl font-bold text-base-content mb-8">Changelog</h1>
      
      <p class="text-base-content/70 mb-8">
        Stay up to date with the latest features, improvements, and bug fixes.
      </p>

      <!-- Loading state -->
      <div v-if="loading" class="flex justify-center py-8">
        <div class="loading loading-spinner loading-lg"></div>
      </div>

      <!-- Error state -->
      <div v-else-if="error" class="alert alert-error mb-8">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>Failed to load changelog entries. Please try again later.</span>
      </div>

      <!-- Changelog entries -->
      <div v-else-if="entries.length > 0" class="space-y-8">
        <article 
          v-for="entry in entries" 
          :key="entry.slug"
          class="border border-base-300 rounded-lg p-6 bg-base-100"
        >
          <!-- Entry header -->
          <div class="flex items-center gap-3 mb-4">
            <span 
              :class="getTypeClass(entry.type)"
              class="badge badge-sm font-medium"
            >
              {{ getTypeLabel(entry.type) }}
            </span>
            <time class="text-sm text-base-content/60">
              {{ formatDate(entry.date) }}
            </time>
          </div>

          <!-- Entry title -->
          <h2 class="text-2xl font-semibold text-base-content mb-3">
            {{ entry.title }}
          </h2>

          <!-- Entry excerpt -->
          <p class="text-base-content/80 mb-4">
            {{ entry.excerpt }}
          </p>

          <!-- Entry content -->
          <div 
            v-if="entry.content"
            class="prose prose-sm max-w-none text-base-content"
            v-html="renderMarkdown(entry.content)"
          ></div>
        </article>
      </div>

      <!-- Empty state -->
      <div v-else class="text-center py-12">
        <div class="text-6xl mb-4">📝</div>
        <h3 class="text-xl font-semibold text-base-content mb-2">No changelog entries yet</h3>
        <p class="text-base-content/60">Check back later for updates and new features.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface ChangelogEntry {
  slug: string
  date: string
  type: 'added' | 'changed' | 'fixed'
  title: string
  excerpt: string
  content: string
}

const entries = ref<ChangelogEntry[]>([])
const loading = ref(true)
const error = ref(false)

const fetchChangelog = async () => {
  try {
    loading.value = true
    error.value = false
    
    const response = await fetch('/api/public/changelog')
    if (!response.ok) {
      throw new Error('Failed to fetch changelog')
    }
    
    const data = await response.json()
    entries.value = data.entries || []
  } catch (err) {
    console.error('Error fetching changelog:', err)
    error.value = true
  } finally {
    loading.value = false
  }
}

const getTypeClass = (type: string) => {
  switch (type) {
    case 'added':
      return 'badge-success'
    case 'changed':
      return 'badge-warning'
    case 'fixed':
      return 'badge-error'
    default:
      return 'badge-neutral'
  }
}

const getTypeLabel = (type: string) => {
  switch (type) {
    case 'added':
      return 'Added'
    case 'changed':
      return 'Changed'
    case 'fixed':
      return 'Fixed'
    default:
      return 'Update'
  }
}

const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch {
    return dateString
  }
}

const renderMarkdown = (content: string) => {
  // Simple markdown rendering with proper Tailwind classes
  return content
    .replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold mt-6 mb-3 text-base-content">$1</h3>')
    .replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold mt-8 mb-4 text-base-content">$1</h2>')
    .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-10 mb-6 text-base-content">$1</h1>')
    .replace(/\*\*(.*?)\*\*/gim, '<strong class="font-semibold text-base-content">$1</strong>')
    .replace(/\*(.*?)\*/gim, '<em class="italic text-base-content">$1</em>')
    .replace(/^\* (.*)$/gim, '<li class="mb-1 text-base-content">$1</li>')
    .replace(/^- (.*)$/gim, '<li class="mb-1 text-base-content">$1</li>')
    .replace(/(<li class="mb-1 text-base-content">.*<\/li>)/gims, '<ul class="list-disc list-inside my-4 space-y-1">$1</ul>')
    .replace(/\n\n/gim, '</p><p class="mb-4 text-base-content">')
    .replace(/^(.*)$/gim, '<p class="mb-4 text-base-content">$1</p>')
    .replace(/^<p class="mb-4 text-base-content"><\/p>$/gim, '')
    .replace(/<p class="mb-4 text-base-content">(<ul[\s\S]*?<\/ul>)<\/p>/gim, '$1')
    .replace(/<p class="mb-4 text-base-content">(<h[1-6][\s\S]*?<\/h[1-6]>)<\/p>/gim, '$1')
}

onMounted(() => {
  fetchChangelog()
})
</script>

<style scoped>
/* Additional styles if needed */
</style>
